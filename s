{"openapi": "3.0.0", "info": {"title": "PromptX API", "version": "1.0.0", "description": "PromptX专业AI能力增强工具"}, "servers": [{"url": "http://*************:3000"}], "paths": {"/mcp": {"post": {"summary": "调用PromptX工具", "operationId": "callPromptXTool", "parameters": [{"name": "Accept", "in": "header", "required": true, "schema": {"type": "string", "default": "application/json, text/event-stream"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC版本", "example": "2.0"}, "method": {"type": "string", "description": "调用方法", "enum": ["tools/list", "tools/call"], "example": "tools/call"}, "params": {"type": "object", "description": "方法参数", "properties": {"name": {"type": "string", "description": "工具名称", "enum": ["promptx_init", "promptx_welcome", "promptx_action", "promptx_learn", "promptx_remember", "promptx_recall"], "example": "promptx_action"}, "arguments": {"type": "object", "description": "工具参数", "example": {"role": "product-manager"}}}}, "id": {"type": "integer", "description": "请求ID", "example": 1}}, "required": ["jsonrpc", "method", "id"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"jsonrpc": {"type": "string"}, "result": {"type": "object"}, "id": {"type": "integer"}}}}}}}}}}}