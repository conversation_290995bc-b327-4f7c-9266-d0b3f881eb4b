{"name": "dpml-prompt", "version": "0.1.0", "description": "DPML-powered AI prompt framework - Revolutionary AI-First CLI system based on Deepractice Prompt Markup Language. Build sophisticated AI agents with structured prompts, memory systems, and execution frameworks.", "main": "src/lib/index.js", "bin": {"dpml-prompt": "src/bin/promptx.js"}, "scripts": {"start": "PROMPTX_ENV=development node src/bin/promptx.js", "test": "jest", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:e2e": "jest --selectProjects e2e", "test:watch": "jest --watch", "test:watchUnit": "jest --watch --selectProjects unit", "test:watchIntegration": "jest --watch --selectProjects integration", "test:coverage": "jest --coverage", "test:coverageUnit": "jest --coverage --selectProjects unit", "test:coverageIntegration": "jest --coverage --selectProjects integration", "test:coverageE2e": "jest --coverage --selectProjects e2e", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests || echo 'Tests completed with some issues'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint src/ --no-error-on-unmatched-pattern || true", "lint:fix": "eslint src/ --fix --no-error-on-unmatched-pattern || true", "format": "echo 'Format skipped - no formatting restrictions'", "format:check": "echo 'Format check skipped - no formatting restrictions'", "validate": "npm run test:ci", "precommit": "echo 'Pre-commit hooks disabled'", "changeset": "changeset", "changeset:version": "changeset version", "changeset:publish": "changeset publish", "changeset:status": "changeset status", "release": "pnpm changeset publish", "release:snapshot": "pnpm changeset version --snapshot snapshot && pnpm changeset publish --tag snapshot", "release:alpha": "pnpm changeset version --snapshot alpha && pnpm changeset publish --tag alpha", "release:beta": "pnpm changeset version --snapshot beta && pnpm changeset publish --tag beta", "version:patch": "pnpm changeset add --type patch", "version:minor": "pnpm changeset add --type minor", "version:major": "pnpm changeset add --type major", "release:version": "standard-version", "release:version:dry": "standard-version --dry-run", "release:version:minor": "standard-version --release-as minor", "release:version:major": "standard-version --release-as major", "release:version:patch": "standard-version --release-as patch", "prepare": "husky install"}, "files": ["src/", "resource/", "package.json", "README.md", "LICENSE", "CHANGELOG.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "@reaxi/node-detect-runtime": "^0.1.0", "body-parser": "^1.20.2", "boxen": "^5.1.2", "chalk": "^4.1.2", "commander": "^11.0.0", "cors": "^2.8.5", "env-paths": "2.2.1", "express": "^5.1.0", "find-monorepo-root": "^1.0.3", "find-pkg-dir": "^2.0.0", "find-up": "^7.0.0", "fs-extra": "^11.1.0", "glob": "^10.3.0", "inquirer": "^8.2.4", "joi": "^17.11.0", "nodemailer": "^7.0.3", "ora": "^5.4.1", "pkg-dir": "^8.0.0", "pnpm": "^10.12.4", "resolve": "^1.22.10", "resolve-package": "^1.0.1", "semver": "^7.5.0", "tree-kill": "^1.2.2", "yaml": "^2.3.0", "zod": "^3.25.62"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/jest": "^29.5.0", "eslint": "^8.42.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-jest": "^27.2.0", "husky": "^8.0.0", "jest": "^29.5.0", "jest-environment-node": "^29.5.0", "lint-staged": "^13.2.0", "prettier": "^2.8.0", "standard-version": "^9.5.0", "supertest": "^6.3.0", "tmp": "^0.2.1"}, "peerDependencies": {"node": ">=14.0.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "keywords": ["dpml", "deepractice", "ai", "prompt", "prompt-engineering", "markup-language", "ai-first", "cli", "chatgpt", "claude", "conversation", "agent", "framework", "role-playing", "memory-system", "automation"], "author": {"name": "Deepractice", "email": "<EMAIL>", "url": "https://deepractice.ai"}, "license": "MIT", "homepage": "https://github.com/Deepractice/PromptX#readme", "repository": {"type": "git", "url": "git+https://github.com/Deepractice/PromptX.git"}, "bugs": {"url": "https://github.com/Deepractice/PromptX/issues"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "lint-staged": {"*.{js,json,md}": ["echo 'Lint-staged disabled'"]}, "husky": {"hooks": {"pre-commit": "echo 'Pre-commit hooks disabled'", "pre-push": "echo 'Pre-push hooks disabled'"}}}