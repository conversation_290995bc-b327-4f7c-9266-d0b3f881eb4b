name: Sync Workflows

on:
  push:
    branches:
      - develop
    paths:
      - '.github/workflows/**'
  workflow_dispatch:
    inputs:
      target_branches:
        description: 'Target branches (comma-separated)'
        default: 'test,main'
        required: true

permissions:
  contents: write

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_PAT || github.token }}
          
      - name: Setup Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
      - name: Sync workflows
        run: |
          # 确定目标分支
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            BRANCHES="${{ github.event.inputs.target_branches }}"
          else
            BRANCHES="test,main"
          fi
          
          echo "Syncing to branches: $BRANCHES"
          
          # 获取最新的workflow相关commit信息
          LATEST_COMMIT_MSG=$(git log -1 --pretty=format:"%s" -- .github/workflows/)
          LATEST_COMMIT_BODY=$(git log -1 --pretty=format:"%b" -- .github/workflows/)
          LATEST_COMMIT_AUTHOR=$(git log -1 --pretty=format:"%an" -- .github/workflows/)
          LATEST_COMMIT_EMAIL=$(git log -1 --pretty=format:"%ae" -- .github/workflows/)
          
          echo "Latest workflow commit: $LATEST_COMMIT_MSG"
          echo "Author: $LATEST_COMMIT_AUTHOR <$LATEST_COMMIT_EMAIL>"
          
          IFS=',' read -ra BRANCH_ARRAY <<< "$BRANCHES"
          for branch in "${BRANCH_ARRAY[@]}"; do
            branch=$(echo "$branch" | xargs)
            echo "Processing branch: $branch"
            
            # 跳过staging分支的自动同步
            if [ "$branch" = "staging" ] && [ "${{ github.event_name }}" = "push" ]; then
              echo "Skipping staging for auto sync"
              continue
            fi
            
            # 检查分支存在
            if ! git ls-remote --heads origin "$branch" | grep -q "$branch"; then
              echo "Branch $branch does not exist"
              continue
            fi
            
            # 切换到目标分支
            git fetch origin "$branch"
            git checkout "$branch"
            
            # 从develop复制workflow文件
            git checkout develop -- .github/workflows/
            
            # 检查是否有变更
            if git diff --cached --quiet; then
              echo "No changes for $branch"
            else
              # 使用原始commit信息，但添加同步标记
              if [ -n "$LATEST_COMMIT_BODY" ]; then
                COMMIT_MSG=$(printf "%s\n\n%s\n\n[workflow-sync] Synced from develop" "$LATEST_COMMIT_MSG" "$LATEST_COMMIT_BODY")
              else
                COMMIT_MSG=$(printf "%s\n\n[workflow-sync] Synced from develop" "$LATEST_COMMIT_MSG")
              fi
              
              # 设置原始作者信息
              git config user.name "$LATEST_COMMIT_AUTHOR"
              git config user.email "$LATEST_COMMIT_EMAIL"
              
              git commit -m "$COMMIT_MSG"
              
              # 恢复bot身份用于push
              git config user.name "github-actions[bot]"
              git config user.email "github-actions[bot]@users.noreply.github.com"
              
              git push origin "$branch"
              echo "✅ Synced to $branch with original commit message"
            fi
            
            # 切回develop
            git checkout develop
          done
          
      - name: Summary
        if: always()
        run: |
          echo "## Sync Summary" >> $GITHUB_STEP_SUMMARY
          echo "Workflow sync completed" >> $GITHUB_STEP_SUMMARY