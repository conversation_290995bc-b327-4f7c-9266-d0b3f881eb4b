<thought protocol="recall-xml" extends="recall">
  <exploration>
    ## 基于通用回忆能力的XML记忆增强
    
    ### 继承核心回忆逻辑
    完全继承 @recall.thought.md 的基础能力：
    - **触发场景**：明确查询、上下文缺失、模式识别、决策支持、个性化服务
    - **信息类型**：身份信息、偏好设置、项目历史、问题解决、关系网络
    - **触发信号**：用户提及过往、问题相似性、个性化需求、历史依据需求
    
    ### XML记忆的特殊处理需求
    - **转义内容还原**：处理 &quot; &gt; &lt; &#x27; 等XML转义字符
    - **结构化信息识别**：技术文档中的层次化内容、代码片段、配置信息
    - **长文本摘要提取**：复杂技术记忆的核心要点快速展示
    - **标签语义增强**：技术标签的语义关联和权重评估
  </exploration>
  
  <reasoning>
    ## 增强的XML记忆检索逻辑
    
    ### 继承并扩展三层检索策略
    
    #### 基础策略（来自原版）+ XML增强
    - **关键词匹配**：直接匹配 + XML结构化关键词支持
    - **语义相关**：理解查询意图 + 技术语义和代码语义理解  
    - **时空关联**：时间项目情境 + 技术栈和项目的关联分析
    
    ### XML特定的相关性评估
    
    #### 在原版评估基础上增加XML维度
    - **直接相关**：完全匹配 + 考虑XML转义后的内容匹配
    - **间接相关**：主题关联 + 技术栈和项目的间接关联
    - **背景相关**：上下文支持 + 历史技术决策的背景信息
    - **结构相关**：XML层次结构中的关联信息
    
    ### 增强的结果组织原则
    
    #### 保持原版组织逻辑 + XML优化
    - **按相关性排序**：最相关优先 + 考虑技术匹配度权重
    - **按时间排序**：新鲜度优先 + 技术时效性考虑
    - **按重要性排序**：用户重要性 + 项目关键程度
    - **分类呈现**：信息类型分组 + 技术内容的智能摘要展示
    
    ### XML内容的渐进展示策略
    - **摘要优先**：提取核心技术要点作为首屏展示
    - **结构化呈现**：保持原有层次但优化可读性
    - **代码美化**：还原转义字符，保持代码格式
    - **按需详情**：复杂内容支持展开查看完整信息
  </reasoning>
  
  <challenge>
    ## 继承原版挑战 + XML特定挑战
    
    ### 原版核心挑战的XML适配
    - **检索准确性问题**：如何避免XML转义导致的匹配失误？
    - **隐私和安全考虑**：技术代码中的敏感信息如何保护？
    - **用户体验挑战**：如何在技术复杂性和展示简洁性间平衡？
    - **系统性能问题**：大量XML技术记忆的检索和渲染性能？
    
    ### XML记忆的独特挑战
    - **内容复杂性**：如何保持技术信息完整性同时避免认知过载？
    - **格式兼容性**：不同平台对XML内容显示能力的差异？
    - **技术时效性**：技术记忆的过期判断和更新提醒？
  </challenge>
  
  <plan>
    ## 继承原版思考结构 + XML增强流程
    
    ### 基础检索思路（继承原版）
    1. 分析查询意图和类型
    2. 应用三层检索策略
    3. 评估结果相关性
    4. 组织和排序信息
    5. 形成回忆结果
    
    ### XML增强处理流程
    1. **XML内容预处理**：检测并标记需要特殊处理的XML内容
    2. **转义内容还原**：将转义字符还原为可读格式
    3. **结构化信息提取**：识别代码块、配置、技术规格等结构
    4. **智能摘要生成**：为复杂技术内容生成核心要点摘要
    5. **渐进式呈现**：根据用户需求选择摘要或详细显示模式
    
    ### 回忆失败的XML特定处理
    - **XML解析失败** → 降级到纯文本检索模式
    - **转义处理错误** → 显示原始内容并标记处理异常
    - **技术内容过期** → 提醒用户信息可能已过时
  </plan>
</thought> 