name: CI

on:
  push:
    branches: [main, develop]
    paths-ignore:
      - 'docs/**'
      - '**/*.md'
      - '.github/**'
      - '!.github/workflows/ci.yml'
      - '.promptx/**'
  pull_request:
    branches: [main, develop]
    paths-ignore:
      - 'docs/**'
      - '**/*.md'
      - '.github/**'
      - '!.github/workflows/ci.yml'
      - '.promptx/**'

permissions:
  contents: read

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node environment
        uses: ./.github/actions/setup-node
        with:
          node-version: ${{ matrix.node-version }}

      - name: Lint code
        run: pnpm lint

      - name: Run unit tests
        run: pnpm test:unit

      - name: Run integration tests
        run: pnpm test:integration

      - name: Run examples tests
        run: pnpm test:examples

      - name: Generate coverage report
        if: matrix.node-version == 20
        run: pnpm test:coverage

      - name: Upload coverage
        if: matrix.node-version == 20
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: ./coverage/lcov.info
          fail_ci_if_error: false