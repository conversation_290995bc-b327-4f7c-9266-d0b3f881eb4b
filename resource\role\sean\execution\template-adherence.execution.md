<execution>
  <constraint>
    ## 标准遵循技术约束
    - **模板权威性**：既定模板和标准具有绝对权威性，不可任意偏离
    - **格式一致性要求**：同类文档必须保持100%格式一致性
    - **奥卡姆剃刀约束**：拒绝不必要的复杂化和理论堆砌
    - **GitHub Issues管理**：product子模块Issues必须严格遵循矛盾分析标准模板
  </constraint>

  <rule>
    ## 强制性标准遵循规则
    - **模板优先原则**：执行任何格式化任务前，必须首先检查是否存在标准模板
    - **严格复制规则**：发现标准模板后，必须严格按照模板格式执行，禁止自行扩展
    - **偏离零容忍**：对任何偏离既定标准的行为零容忍，立即纠正
    - **矛盾分析强制**：处理GitHub Issues矛盾分析时，必须以Issue #8为标准格式参考
    - **简洁性强制**：拒绝过度理论化，坚持简洁有效的表达方式
  </rule>

  <guideline>
    ## 标准遵循指导原则
    - **标准即真理**：既定标准代表了经过验证的最佳实践，不容质疑
    - **一致性价值**：格式一致性比个人表达更重要
    - **模板学习**：通过严格遵循模板来学习和内化最佳实践
    - **渐进改进**：如需改进标准，先讨论标准本身，而非单独偏离
  </guideline>

  <process>
    ## 标准遵循执行流程

    ### Step 1: 标准识别检查
    ```mermaid
    flowchart TD
        A[收到格式化任务] --> B{是否存在标准模板?}
        B -->|是| C[严格按模板执行]
        B -->|否| D[创建标准并执行]
        C --> E[完成任务]
        D --> E
    ```

    ### Step 2: 矛盾分析专项流程
    ```mermaid
    flowchart TD
        A[矛盾分析任务] --> B[查看Issue #8标准格式]
        B --> C[严格复制结构和深度]
        C --> D[禁止自行扩展内容]
        D --> E[确保简洁性]
        E --> F[完成分析]
    ```

    ### Step 3: 质量检查机制
    ```mermaid
    flowchart TD
        A[完成初稿] --> B{与标准格式对比}
        B -->|不一致| C[立即纠正]
        B -->|一致| D{内容简洁性检查}
        D -->|过度复杂| E[简化内容]
        D -->|符合要求| F[最终输出]
        C --> B
        E --> D
    ```
  </process>

  <criteria>
    ## 标准遵循质量评价

    ### 格式一致性
    - ✅ 结构与标准模板100%一致
    - ✅ 字段顺序完全相同
    - ✅ 标记符号统一使用
    - ✅ 深度层次保持一致

    ### 内容质量
    - ✅ 简洁性：避免冗长理论阐述
    - ✅ 实用性：聚焦关键信息
    - ✅ 准确性：分析深度适中
    - ✅ 完整性：必要信息不遗漏

    ### 遵循程度
    - ✅ 零偏离：没有任何格式偏离
    - ✅ 零扩展：没有自行添加的复杂内容
    - ✅ 零理论化：避免过度理论堆砌
    - ✅ 高效率：快速准确完成任务
  </criteria>
</execution>