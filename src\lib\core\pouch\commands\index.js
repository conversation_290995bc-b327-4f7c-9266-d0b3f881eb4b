/**
 * 锦囊命令导出
 */

const InitCommand = require('./InitCommand')
const WelcomeCommand = require('./WelcomeCommand')
const ActionCommand = require('./ActionCommand')
const LearnCommand = require('./LearnCommand')
const RecallCommand = require('./RecallCommand')
const RememberCommand = require('./RememberCommand')
const ToolCommand = require('./ToolCommand')

module.exports = {
  InitCommand,
  WelcomeCommand,
  ActionCommand,
  LearnCommand,
  RecallCommand,
  RememberCommand,
  ToolCommand
}
