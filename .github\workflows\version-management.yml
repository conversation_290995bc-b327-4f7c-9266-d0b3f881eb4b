name: Version Management

on:
  workflow_dispatch:
    inputs:
      target_branch:
        description: 'Target branch to create version PR (test/staging/main)'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - staging
          - main
      release_type:
        description: 'Release type (auto/patch/minor/major)'
        required: true
        default: 'auto'
        type: choice
        options:
          - auto
          - patch
          - minor
          - major

permissions:
  contents: write
  pull-requests: write

jobs:
  prepare-version:
    name: Prepare Version Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_PAT || github.token }}
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
      
      - name: Create version branch
        run: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          BRANCH_NAME="chore/release-${TIMESTAMP}"
          git checkout -b $BRANCH_NAME
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
      
      - name: Create release marker
        run: |
          # 创建标记文件，包含发布信息
          cat > .release-pending.json << EOF
          {
            "release_type": "${{ github.event.inputs.release_type }}",
            "target_branch": "${{ github.event.inputs.target_branch }}",
            "triggered_by": "${{ github.actor }}",
            "triggered_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
          }
          EOF
          
          git add .release-pending.json
          git commit -m "chore: prepare release to ${{ github.event.inputs.target_branch }} [${{ github.event.inputs.release_type }}]"
      
      - name: Push version branch
        run: |
          git push origin ${{ env.BRANCH_NAME }}
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT || github.token }}
      
      - name: Create Pull Request
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_PAT || github.token }}
          script: |
            const { data: pr } = await github.rest.pulls.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `chore: prepare release to ${{ github.event.inputs.target_branch }} [${{ github.event.inputs.release_type }}]`,
              head: `${{ env.BRANCH_NAME }}`,
              base: '${{ github.event.inputs.target_branch }}',
              body: `## 🚀 版本发布准备
              
              此PR将在合并时自动执行版本升级和发布。
              
              ### 📋 发布信息
              - **目标分支**: ${{ github.event.inputs.target_branch }}
              - **发布类型**: ${{ github.event.inputs.release_type }}
              - **触发者**: @${{ github.actor }}
              
              ### ⏳ 等待预览
              GitHub Actions 将在几秒内生成版本变更预览...
              
              ### ✅ 合并后将自动执行
              1. 根据commit历史计算版本号
              2. 生成CHANGELOG.md  
              3. 提交版本变更
              4. 触发对应的发布流程
              
              ### 🔄 发布流程
              - 合并到 \`test\` → 触发 \`npm-alpha-release.yml\` → 发布 \`alpha\` 版本
              - 合并到 \`staging\` → 触发 \`npm-beta-release.yml\` → 发布 \`beta\` 版本
              - 合并到 \`main\` → 触发 \`npm-latest-release.yml\` → 发布正式版本
              
              > ⚠️ **注意**: 请等待版本预览生成后再审核此PR`
            });
            
            console.log(`Created PR #${pr.number}: ${pr.html_url}`);