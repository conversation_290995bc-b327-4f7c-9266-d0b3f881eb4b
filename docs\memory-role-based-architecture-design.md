# PromptX 记忆系统角色分层架构设计文档

> **版本**: v1.0  
> **日期**: 2025年1月  
> **状态**: 设计方案确定  

## 🎯 **设计背景与目标**

### **当前问题**
- 单一记忆文件 (`.promptx/memory/declarative.dpml`) 存储所有记忆
- 不同角色的记忆混杂在一起，容易产生记忆污染
- AI激活不同角色时无法获得专属的专业记忆
- 缺乏角色专业化的记忆管理机制

### **设计目标**
- 实现**一个角色一套记忆**的分层架构
- 保持记忆作为**提示词片段**的本质，避免过度复杂化
- 为AI用户提供简洁有效的上下文，避免上下文丢失
- **最小化架构改动**，保持现有系统的简洁性
- **利用Action锦囊的天然边界**，实现优雅的记忆管理机制

## 🏗️ **核心设计原则**

### **业务原则**
1. **记忆 = 提示词片段**：记忆的本质是为AI提供上下文
2. **简洁 > 功能丰富**：避免过度工程化，保持系统可理解性
3. **记忆完全个人化**：每个角色拥有独立记忆空间，拒绝共享记忆概念
4. **AI直接消费**：记忆格式应该让AI可以直接理解和使用
5. **模拟真实认知**：符合人类专家的真实记忆模式，不存在"共享大脑"

### **技术原则**
1. **最小化改动**：仅调整文件存储路径，保持现有代码逻辑
2. **保持文件格式**：继续使用declarative.dpml文件名和XML格式
3. **向后兼容**：现有记忆可平滑迁移到新结构

## 📁 **存储架构设计**

### **目录结构**
```
.promptx/memory/
├── java-developer/
│   └── declarative.dpml     # Java开发专家记忆
├── product-manager/
│   └── declarative.dpml     # 产品经理记忆
├── copywriter/
│   └── declarative.dpml     # 文案专家记忆
├── nuwa/
│   └── declarative.dpml     # 女娲角色记忆
└── sean/
    └── declarative.dpml     # Sean角色记忆
```

**设计特点**：
- **一个角色一个目录**：角色ID直接作为目录名，结构扁平清晰
- **纯粹个人化记忆**：每个角色拥有完全独立的记忆空间，符合人类认知模式
- **保持文件名一致**：继续使用`declarative.dpml`，无需学习新命名
- **最小化改动**：只是将单文件拆分为多目录，核心逻辑不变

### **记忆文件格式**
保持现有XML DPML格式，按角色分文件存储：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<memory role="java-developer">
  <item id="java_001" time="2025/01/15 14:30">
    <content>Spring Boot性能优化：使用@Lazy注解延迟加载重型Bean，启动时间减少40%</content>
    <tags>性能优化 Spring Boot 最佳实践</tags>
  </item>
</memory>
```

## 🔄 **业务逻辑设计**

### **1. Remember 保存逻辑**

#### **设计决策**
- **存储到当前激活角色**：这是唯一逻辑，确保记忆的角色专属性和纯粹性
- **拒绝共享记忆概念**：每个角色的记忆完全独立，符合人类认知现实
- **保持简单判断**：避免复杂的内容分析算法

#### **实施方案**
```
用户使用remember → 获取当前激活角色 → 存储到{current-role}/declarative.dpml
```

#### **认知科学依据**
就像人类现实中每个专家的记忆都是完全个人化的：
- 即使共同经历，每个人记住的细节、感受都不同
- 不存在真正的"共享大脑记忆"
- 公共知识存在于书籍、文档等外部载体中（对应PromptX的resource系统）

### **2. Recall 检索逻辑**

#### **设计决策**
- **仅查当前角色记忆**：保持记忆的纯粹性和角色专属性
- **拒绝跨角色检索**：避免记忆污染，确保角色边界清晰
- **专业化回忆**：AI只基于当前角色的历史经验进行回忆

#### **认知模型依据**
符合人类专家的真实记忆模式：
1. **专家回忆自己的经验**：Java专家回忆自己的编程经验，不会想起产品经理的决策经历
2. **记忆边界清晰**：每个专家只能访问自己的记忆库
3. **专业化程度高**：回忆结果完全针对当前专业领域

#### **实施流程**
```
用户query → 检索{current-role}/declarative.dpml → 返回纯粹的角色专业记忆
```

#### **"公共知识"的获取方式**
通过其他机制满足知识共享需求：
- **learn命令**：学习相同的思维模式或执行方法
- **resource系统**：访问相同的知识资源和文档
- **协作机制**：未来通过专门的协作模块实现信息传递

### **3. Action 角色激活逻辑** ⭐

#### **核心设计亮点**
**Action锦囊同时加载角色定义和角色记忆**，一次激活完成完整的专业身份转换。

#### **完整激活流程**
```
用户: "激活Java开发专家"
↓
Action命令执行:
1. 加载角色定义 (java-developer.role.md)
2. 读取角色记忆 (java-developer/declarative.dpml)  
3. 组合输出: 角色能力 + 专业记忆上下文
↓
AI获得完整专业身份: 思维模式 + 历史经验
```

#### **优雅解决的问题**
- **✅ 角色切换上下文**: Action激活新角色时自动加载新记忆，天然完成上下文切换
- **✅ 当前角色获取**: Action命令本身就知道正在激活哪个角色ID
- **✅ 记忆边界控制**: 每次Action只加载对应角色的记忆，自然隔离
- **✅ 记忆时效性**: 角色激活时记忆立即生效，无需额外操作

## 💫 **设计亮点总结**

**Action锦囊作为记忆边界的天然分界点**，这个设计的优雅之处在于：

1. **自然流畅**: 角色激活和记忆加载在同一个动作中完成
2. **边界清晰**: 每个角色的记忆空间完全独立，Action命令自动控制边界
3. **上下文连贯**: 角色身份和专业记忆同时激活，形成完整专业人格
4. **符合直觉**: 就像人类专家被"唤醒"时同时想起相关经验
5. **实施简洁**: 无需复杂的状态管理，利用现有Action流程自然实现

## ✅ **关键问题解决方案**

### **1. 记忆归属边界** ✅ **已解决**
- **解决方案**：**完全按角色分离，拒绝共享记忆概念**
- **原理**：模拟人类真实认知 - 每个专家只有自己的记忆，没有"共享大脑"
- **好处**：边界清晰、设计纯粹、符合直觉
- **跨角色知识需求**：通过learn命令和resource系统解决

### **3. 角色切换上下文处理** ✅ **已解决**
- **解决方案**：**Action锦囊同时加载角色记忆机制**
- **实现方式**：Action激活角色时同步加载对应记忆，完成完整的上下文切换
- **效果**：AI在角色切换时自然获得新的专业身份和相关记忆，无需额外处理
- **优势**：符合人类专家被"唤醒"时同时想起相关经验的认知模式

### **4. 当前角色获取机制** ✅ **已解决**
- **解决方案**：**Action命令天然知道当前激活角色ID**
- **实现方式**：Remember/Recall通过Action命令的执行上下文获取当前角色
- **效果**：无需额外的状态管理机制，角色ID在Action执行过程中天然可用
- **优势**：保持系统简洁，避免引入额外的状态文件或复杂机制

## 🚀 **实施路径规划**

### **核心改动内容**
1. **目录结构调整**：创建纯粹的角色目录结构
2. **Action命令增强**：同时加载角色定义和角色记忆
3. **数据迁移工具**：将现有单文件记忆按角色分类迁移
4. **Remember/Recall路径调整**：根据当前激活角色操作对应目录

### **具体实施步骤**
1. **ActionCommand修改**：在激活角色时同时读取并输出角色记忆
2. **RememberCommand修改**：保存时根据当前角色选择目标目录
3. **RecallCommand修改**：检索时仅查当前角色目录，保持记忆纯粹性  
4. **迁移脚本编写**：分析现有记忆内容，按角色特征分配到对应目录
5. **兼容性测试**：确保现有功能正常，XML格式保持一致

### **完整的记忆生命周期**
```
Action激活角色 → 同时加载角色记忆 → AI获得纯粹专业上下文
        ↓
   用户专业对话交互
        ↓  
Remember保存新记忆 → 存储到当前角色目录
        ↓
Recall检索相关记忆 → 从当前角色记忆中检索
        ↓
   纯粹专业化积累（每个角色独立成长）
```

## 🎯 **成功标准**

### **功能标准**
- ✅ 不同角色拥有完全独立的记忆空间
- ✅ 记忆检索仅限当前角色，保持记忆纯粹性
- ✅ **Action激活角色时同时加载专业记忆**，形成纯粹专业上下文
- ✅ 保持现有XML格式的兼容性

### **体验标准** 
- ✅ AI回答更具专业性和针对性
- ✅ 记忆不会在角色间产生污染
- ✅ 用户可以清楚理解记忆的归属和使用逻辑
- ✅ 系统保持简洁，避免过度复杂化

### **技术标准**
- ✅ 代码修改最小化，保持向后兼容
- ✅ 文件格式保持不变，迁移过程平滑
- ✅ 性能不因分层而显著下降

## 📝 **附录**

### **相关理论支撑**
- **ACT-R认知理论**：声明性记忆 vs 程序性记忆的分层管理
- **PromptX设计哲学**：记忆作为提示词片段的本质定位
- **AI上下文工程**：为AI提供结构化、可消费的专业知识

### **参考资源**
- [PromptX架构原理文档](promptx-architecture-principle.md)
- [现有记忆系统实现分析](../src/lib/core/pouch/commands/)
- [DPML协议规范](../resource/protocol/dpml.protocol.md)

---

**文档状态**: 设计方案完成，核心问题已优雅解决  
**核心突破**: 
- Action锦囊同时加载角色记忆的设计完美解决了角色切换和状态管理问题
- 去除global记忆概念，实现纯粹个人化记忆，符合人类真实认知模式
**设计哲学**: "公共记忆那不就是书籍嘛" - 真正的记忆应该是完全个人化的  
**下一步**: 开始实施Action命令增强和纯粹角色目录结构调整 