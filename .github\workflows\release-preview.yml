name: Release Preview

on:
  pull_request:
    types: [opened, synchronize]
    branches:
      - test
      - staging
      - main

jobs:
  preview:
    name: Generate Release Preview
    if: github.head_ref == 'develop' || startsWith(github.head_ref, 'release/') || startsWith(github.head_ref, 'chore/release-')
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      
    steps:
      - name: Checkout PR branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.ref }}
          
      - name: Checkout base branch
        run: |
          git fetch origin ${{ github.event.pull_request.base.ref }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate version preview
        id: preview
        run: |
          # 获取当前版本
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          
          # 检查是否有release标记文件
          RELEASE_TYPE="auto"
          if [ -f ".release-pending.json" ]; then
            RELEASE_TYPE=$(jq -r '.release_type' .release-pending.json)
            echo "Found release marker with type: $RELEASE_TYPE"
          fi
          echo "release_type=$RELEASE_TYPE" >> $GITHUB_OUTPUT
          
          # 设置git配置用于预览
          git config user.name "preview-bot"
          git config user.email "<EMAIL>"
          
          # 预览版本变更（不实际修改文件）
          echo "=== Version Preview ===" > preview.log
          if [ "$RELEASE_TYPE" = "auto" ]; then
            pnpm release:version:dry >> preview.log 2>&1 || true
          else
            pnpm release:version:dry -- --release-as $RELEASE_TYPE >> preview.log 2>&1 || true
          fi
          
          # 提取新版本号
          NEW_VERSION=$(grep "bumping version" preview.log | tail -1 | sed -E 's/.*to ([0-9]+\.[0-9]+\.[0-9]+).*/\1/' || echo "$CURRENT_VERSION")
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          
          # 生成临时的版本和changelog
          if [ "$NEW_VERSION" != "$CURRENT_VERSION" ]; then
            # 创建临时分支进行预览
            git checkout -b temp-preview-${{ github.event.pull_request.number }}
            
            # 执行版本升级
            if [ "$RELEASE_TYPE" = "auto" ]; then
              pnpm release:version --yes
            else
              pnpm release:version:$RELEASE_TYPE --yes
            fi
            
            # 提取CHANGELOG内容
            if [ -f "CHANGELOG.md" ]; then
              # 获取最新版本的changelog内容
              CHANGELOG_CONTENT=$(awk "/^## \[$NEW_VERSION\]/ {flag=1; next} /^## \[/ {flag=0} flag" CHANGELOG.md)
              
              # 保存到输出
              {
                echo 'changelog<<EOF'
                echo "$CHANGELOG_CONTENT"
                echo 'EOF'
              } >> $GITHUB_OUTPUT
            else
              echo "changelog=No CHANGELOG.md found" >> $GITHUB_OUTPUT
            fi
            
            # 获取提交摘要
            COMMITS=$(git log origin/${{ github.event.pull_request.base.ref }}..HEAD --oneline --no-merges)
            {
              echo 'commits<<EOF'
              echo "$COMMITS"
              echo 'EOF'
            } >> $GITHUB_OUTPUT
          else
            echo "changelog=No version change detected" >> $GITHUB_OUTPUT
            echo "commits=" >> $GITHUB_OUTPUT
          fi
          
      - name: Comment on PR
        uses: actions/github-script@v7
        with:
          script: |
            const currentVersion = '${{ steps.preview.outputs.current_version }}';
            const newVersion = '${{ steps.preview.outputs.new_version }}';
            const changelog = `${{ steps.preview.outputs.changelog }}`;
            const commits = `${{ steps.preview.outputs.commits }}`;
            
            let body = `## 🚀 发版预览\n\n`;
            
            if (newVersion !== currentVersion) {
              body += `### 📦 版本变更\n`;
              body += `- **当前版本**: \`${currentVersion}\`\n`;
              body += `- **新版本**: \`${newVersion}\`\n`;
              body += `- **发布标签**: \`alpha\`\n\n`;
              
              if (changelog && changelog !== 'No version change detected') {
                body += `### 📝 CHANGELOG 预览\n`;
                body += `${changelog}\n\n`;
              }
              
              if (commits) {
                body += `### 📋 包含的提交\n`;
                body += `\`\`\`\n${commits}\n\`\`\`\n\n`;
              }
              
              body += `### ✅ 合并后将自动执行\n`;
              body += `1. 版本号升级到 \`${newVersion}\`\n`;
              body += `2. 更新 CHANGELOG.md\n`;
              body += `3. 发布 \`${newVersion}-alpha.0\` 到 npm\n`;
              body += `4. 创建 Git tag: \`v${newVersion}-alpha\`\n\n`;
              
              body += `### 📥 安装命令预览\n`;
              body += `\`\`\`bash\n`;
              body += `npm install dpml-prompt@alpha\n`;
              body += `# 或指定版本\n`;
              body += `npm install dpml-prompt@${newVersion}-alpha.0\n`;
              body += `\`\`\`\n`;
            } else {
              body += `### ℹ️ 无版本变更\n`;
              body += `当前 PR 不包含需要版本升级的改动。\n\n`;
              
              body += `**提示**: 版本升级通常由以下类型的提交触发：\n`;
              body += `- \`feat:\` 新功能 → minor 版本升级\n`;
              body += `- \`fix:\` 修复 → patch 版本升级\n`;
              body += `- \`feat!:\` 或 \`fix!:\` 破坏性变更 → major 版本升级\n`;
            }
            
            body += `\n---\n`;
            body += `*此预览由 GitHub Actions 自动生成 • [工作流运行详情](${context.serverUrl}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})*`;
            
            // 查找现有的预览评论
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });
            
            const botComment = comments.find(comment => 
              comment.user.type === 'Bot' && 
              comment.body.includes('🚀 发版预览')
            );
            
            if (botComment) {
              // 更新现有评论
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: body
              });
            } else {
              // 创建新评论
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: body
              });
            }