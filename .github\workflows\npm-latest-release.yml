name: NPM Latest Release

on:
  # push:
  #   branches:
  #     - main
  #   paths-ignore:
  #     # GitHub相关配置
  #     - '.github/**'
  #     # 文档目录
  #     - 'docs/**'
  #     # 根目录的README文件（但不包括prompt/和resource/下的.md文件）
  #     - 'README*.md'
  #     - 'CHANGELOG.md'
  #     - 'TEST_ALPHA.md'
  #     # 配置文件
  #     - 'LICENSE'
  #     - '.gitignore'
  #     - '.npmignore'
  #     - '.cursorrules'
  #     - 'commitlint.config.js'
  #     # 资源文件
  #     - 'assets/**'
  #     # 测试覆盖率报告
  #     - 'coverage/**'
  # 注意：main分支(latest通道)的自动发布已禁用，保护线上用户
  # 需要发布正式版本时请使用手动触发
  workflow_dispatch:

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # This makes Actions fetch all Git history so that Changesets can generate changelogs with the correct commits
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci

      - name: Create Release Pull Request or Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          # This expects you to have a script called release which does a build for your packages and calls changeset publish
          publish: pnpm changeset publish
          title: 'chore: release package'
          commit: 'chore: release package'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }} 